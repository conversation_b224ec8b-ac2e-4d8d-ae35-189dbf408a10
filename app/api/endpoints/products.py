from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from app.utils.logging import setup_logging
from app.utils.db_utils import (
    get_numbers_of_categories,
    check_existence_of_product_ids,
    check_existence_of_attribute_ids,
)
from app.core.processor_registry import registry
import asyncio
from typing import Optional, Dict, List
import time
import json
import warnings

router = APIRouter()
logger = setup_logging()

import os
from dotenv import load_dotenv

load_dotenv()
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")


async def validate_request(
    query_product_ids: List[int] = Body(...),
    scope_product_id: Optional[List[int]] = Body([]),
    weights: Optional[Dict[str, float]] = Body({}),
    database: Optional[str] = Body(""),
):
    """Validate incoming request parameters"""
    log = logger.bind(product_ids=query_product_ids)

    valid_scope_ids = None
    missing_sids = []
    valid_atrids = weights

    processor = await registry.get_or_create(database)
    try:
        if not query_product_ids:
            return {"categories": []}

        categories = get_numbers_of_categories(query_product_ids, database=processor.pg)
        missing_pids = check_existence_of_product_ids(
            product_ids=query_product_ids, database=processor.pg
        )
        if scope_product_id:
            missing_sids = check_existence_of_product_ids(
                product_ids=scope_product_id, database=processor.pg
            )
            valid_scope_ids = [
                pid for pid in scope_product_id if pid not in missing_sids
            ]

        if len(categories) > 1:
            log.error("request.validation.multiple_categories", categories=categories)
            raise HTTPException(
                status_code=400, detail="Multiple categories not supported"
            )
        if weights:
            atrids = [int(k) for k in weights.keys()]
            missing_atrids = check_existence_of_attribute_ids(
                atrids, categories[0], processor.pg
            )
            if missing_atrids == -1:
                log.error("request.validation.all_atrids_missing")
                raise HTTPException(
                    status_code=400,
                    detail=f"All attribute ids provided are missing in the {database} database",
                )
            if missing_atrids:
                log.warning(
                    "request.validation.missing_atrids", missing_atrids=missing_atrids
                )
                warnings.warn(
                    "One or more than one attribute id is missing", RuntimeWarning
                )
                valid_atrids = {
                    key: value
                    for key, value in weights.items()
                    if key not in missing_atrids
                }

        valid_product_ids = [
            pid for pid in query_product_ids if pid not in missing_pids
        ]
        if len(missing_pids) >= 1:
            log.warning("request.validation.missing_pids", missing_pids=missing_pids)
            warnings.warn(
                "Multiple missing PIDs detected. Please check the logs for details.",
                RuntimeWarning,
            )

        if len(missing_sids) >= 1:
            log.warning("request.validation.missing_pids", missing_sids=missing_sids)
            warnings.warn(
                "Multiple missing PIDs detected. Please check the logs for details.",
                RuntimeWarning,
            )

        log.info("request.validation.success", categories=categories[0])
        return {
            "categories": categories[0],
            "valid_product_ids": valid_product_ids,
            "valid_scope_ids": valid_scope_ids,
            "valid_atrids": valid_atrids,
        }

    except Exception as e:
        log.error("request.validation.failed", error=str(e))
        raise


async def process_similar_products(
    query_product_ids: List[int],
    categories: List[int],
    database: Optional[str] = Body(None),
    weights: Optional[dict] = None,
    top_k: int = 5,
    scope_product_id: Optional[int] = None,
):
    processor = await registry.get_or_create(database)
    """Core processing function for similar products"""
    return await asyncio.to_thread(
        processor.inference.search_with_exist_products,
        query_product_ids,
        categories,
        weights,
        top_k,
        scope_product_id,
    )


@router.post("/get-similar-products-by-product-id")
async def get_similar_products(
    query_product_ids: List[int],
    database: Optional[str] = Body(default=""),
    weights: Optional[Dict[str, float]] = None,
    top_k: int = 5,
    scope_product_id: Optional[List[int]] = None,
    request_data: dict = Depends(validate_request),
):
    valid_product_ids = request_data["valid_product_ids"]
    valid_scope_ids = request_data["valid_scope_ids"]
    weights = request_data["valid_atrids"]
    processor = await registry.get_or_create(database)
    if weights is not None and len(weights) > 0:
        weights = {int(k): v for k, v in weights.items()}
    else:
        weights = None

    log = logger.bind(
        product_ids=valid_product_ids,
        categories=request_data["categories"],
        top_k=top_k,
    )
    category = request_data["categories"]
    start_time = time.time()
    try:
        products = await processor.queue_request(
            process_similar_products,
            valid_product_ids,
            category,
            database,
            weights,
            top_k,
            valid_scope_ids,
        )

        process_time = time.time() - start_time
        log.info(
            "similar_products.success",
            process_time=f"{process_time:.4f}s",
            results_count=len(products),
            result=products,
        )

        return JSONResponse(content=jsonable_encoder(products), status_code=200)

    except Exception as e:
        process_time = time.time() - start_time
        log.error(
            "similar_products.failed", error=str(e), process_time=f"{process_time:.4f}s"
        )
        raise


@router.post("/get-similar-products-by-specs")
async def get_similar_products_by_specs(
    specs: list[dict[str, str]],
    product_category_id: int,
    weights: Optional[dict] = None,
    top_k: int = 5,
    scope_query: Optional[str] = None,
    _: None = Depends(validate_request),
):
    # Implementation for specs-based search
    raise HTTPException(
        status_code=501, detail="Specs-based search not implemented yet"
    )


@router.get("/get-available-databases")
async def get_available_databases():
    with open(os.path.join(BASE_DIRECTORY, "databases.json"), "r") as f:
        databases = json.load(f)
    return JSONResponse(content=databases, status_code=200)


@router.get("/get-specific-search-scope-databases")
async def get_specific_search_scope_databases():
    pass
